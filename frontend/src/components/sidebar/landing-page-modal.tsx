'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogTitle,
} from '@/components/ui/dialog';
import { useMediaQuery } from '@/hooks/use-media-query';
import { Home, ExternalLink } from 'lucide-react';
import Link from 'next/link';

interface LandingPageModalProps {
  open: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

export function LandingPageModal({ open, onOpenChange }: LandingPageModalProps) {
  const [setOpen] = useState(false);
  const isDesktop = useMediaQuery('(min-width: 768px)');

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="w-full justify-start text-xs">
          <Home className="h-4 w-4 mr-2" />
          Landing Page
        </Button>
      </DialogTrigger>
      <DialogContent className="p-0 gap-0 border-none max-w-[90vw] max-h-[90vh] rounded-xl overflow-hidden">
        <DialogTitle className="sr-only">
          Atlas Landing Page
        </DialogTitle>
        <div className="h-[80vh] w-full">
          {/* Header */}
          <div className="p-4 border-b bg-background flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Home className="h-5 w-5 text-primary" />
              <h2 className="text-lg font-semibold">Atlas Landing Page</h2>
            </div>
            <div className="flex items-center gap-2">
              <Link href="/landing" target="_blank">
                <Button variant="outline" size="sm" className="gap-2">
                  <ExternalLink className="h-4 w-4" />
                  Open in New Tab
                </Button>
              </Link>
            </div>
          </div>
          
          {/* Iframe Content */}
          <div className="h-full w-full">
            <iframe
              src="/landing"
              className="w-full h-full border-0"
              title="Atlas Landing Page"
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
