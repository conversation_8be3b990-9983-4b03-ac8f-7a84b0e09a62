import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, Search } from "lucide-react";
import Features from "@/components/features-11";
import Link from "next/link";
import HeroSectionFive from "@/components/hero-section-five";
import { SectionBadge } from '@/components/ui/section-badge';
import IntegrationsSection from "@/components/integrations-2";
import TestimonialsSection from "@/components/testimonials";
import FAQSection from "@/components/faq";
import Image from "next/image";
import { Card } from "@/components/ui/card";
import { BookDemoSection } from "@/components/home/<USER>/book-demo-section";

export default async function LandingPage() {

  return (
    <div className="flex flex-col min-h-screen bg-background text-foreground">
      <HeroSectionFive />

      <main className="flex-1 w-full mx-auto px-4 sm:px-6 lg:px-8 flex flex-col gap-8 sm:gap-12 md:gap-16 items-center">{/* Content sections start here */}

        {/* Features Section (Bento Grid) */}
        <section id="features">
          <Features />
        </section>

        {/* Use Cases Section */}
        <section id="use-cases" className="w-full max-w-6xl py-8 sm:py-12 md:py-16 px-4 sm:px-6">
          <SectionBadge>Use Cases</SectionBadge>

          <div className="flex justify-center mt-6 mb-8 sm:mb-10">
            <Link href="/cases">
              <Button variant="default" className="gap-2 rounded-full px-6 text-base">
                <Search className="h-4 w-4" />
                <span>View all use cases</span>
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8">
            <div className="space-y-6 sm:space-y-8">
              {/* Research & Analysis Card */}
              <div className="relative overflow-hidden rounded-xl sm:rounded-2xl border bg-card text-card-foreground shadow-sm">
                <div className="p-6 sm:p-8">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Search className="h-5 w-5 text-primary" />
                    </div>
                    <h3 className="text-lg sm:text-xl font-semibold">Research & Analysis</h3>
                  </div>
                  <p className="text-muted-foreground mb-4 text-sm sm:text-base">
                    Conduct comprehensive research, analyze data, and generate insights across multiple sources and formats.
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>Multi-source data gathering</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>Automated report generation</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>Real-time insights</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Content Creation Card */}
              <div className="relative overflow-hidden rounded-xl sm:rounded-2xl border bg-card text-card-foreground shadow-sm">
                <div className="p-6 sm:p-8">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Search className="h-5 w-5 text-primary" />
                    </div>
                    <h3 className="text-lg sm:text-xl font-semibold">Content Creation</h3>
                  </div>
                  <p className="text-muted-foreground mb-4 text-sm sm:text-base">
                    Generate high-quality content, documents, and presentations with AI-powered assistance.
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>Document generation</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>Content optimization</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>Multi-format output</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-6 sm:space-y-8">
              {/* Automation & Workflows Card */}
              <div className="relative overflow-hidden rounded-xl sm:rounded-2xl border bg-card text-card-foreground shadow-sm">
                <div className="p-6 sm:p-8">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Search className="h-5 w-5 text-primary" />
                    </div>
                    <h3 className="text-lg sm:text-xl font-semibold">Automation & Workflows</h3>
                  </div>
                  <p className="text-muted-foreground mb-4 text-sm sm:text-base">
                    Automate repetitive tasks and create efficient workflows to boost productivity.
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>Task automation</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>Workflow optimization</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>Integration capabilities</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* External Research Image Card */}
              <div className="relative overflow-hidden rounded-xl sm:rounded-2xl border bg-card text-card-foreground shadow-sm">
                <div className="aspect-[4/3]">
                  <Image
                    src="https://framerusercontent.com/images/a9T3OzH5SP0FlLLqfv12ZJV3do.webp?scale-down-to=1024"
                    alt="External Research Demo"
                    className="w-full h-full object-cover"
                    fill
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Integrations Section */}
        <IntegrationsSection />

        {/* Testimonials Section */}
        <TestimonialsSection />

        {/* FAQ Section */}
        <FAQSection />

        {/* Book Demo Section */}
        <BookDemoSection />

        {/* CTA Card Section */}
        <section className="w-full flex justify-center items-end my-8 sm:my-12 mb-16 sm:mb-24 px-4 sm:px-6 md:px-8">
          <div className="w-full max-w-6xl">
            <SectionBadge>Try Atlas</SectionBadge>
            <Card className="py-6 sm:py-8 md:py-12 px-4 sm:px-6 md:px-12 rounded-xl sm:rounded-2xl relative overflow-hidden bg-transparent border-0">
              <div className="absolute inset-0 w-full h-full">
                <Image
                  src="/violet.png"
                  alt="Background pattern"
                  fill
                  className="object-cover !relative"
                  priority
                  unoptimized
                />
              </div>
              <div className="relative z-20 flex flex-col items-center">
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold tracking-tight text-center mb-4 sm:mb-6 text-white mix-blend-difference px-2">
                  Ready to transform your workflow?
                </h2>
                <p className="text-base sm:text-lg text-center mb-6 sm:mb-8 text-white/90 mix-blend-difference max-w-2xl px-2">
                  Join thousands of professionals who are already using Atlas to automate their tasks and boost productivity.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto">
                  <Link href="/dashboard">
                    <Button size="lg" className="w-full sm:w-auto bg-white text-black hover:bg-white/90 font-medium px-6 sm:px-8">
                      Get Started Free
                    </Button>
                  </Link>
                  <Link href="/cases">
                    <Button variant="outline" size="lg" className="w-full sm:w-auto border-white text-white hover:bg-white hover:text-black font-medium px-6 sm:px-8">
                      View Use Cases
                    </Button>
                  </Link>
                </div>
              </div>
            </Card>
          </div>
        </section>

      </main>
    </div>
  );
}
